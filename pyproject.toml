[project]
name = "keywordsv1"
version = "0.1.0"
description = "Campaign Keywords Pipeline - AI-powered patient keyword extraction using Google Gemini"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "<PERSON><PERSON>ani"}
]
keywords = ["healthcare", "ai", "nlp", "keywords", "gemini", "patient-data"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Healthcare Industry",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "google-generativeai>=0.3.0",  # Google Gemini API client
    "pyodbc>=4.0.39",              # SQL Server database connectivity
    "pandas>=2.0.0",               # Data manipulation and analysis
    "tenacity>=8.2.0",             # Retry logic for API calls
    "tiktoken>=0.5.0",             # Token counting for cost estimation
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/keywordsv1"
Repository = "https://github.com/your-org/keywordsv1"
Documentation = "https://github.com/your-org/keywordsv1#readme"
Issues = "https://github.com/your-org/keywordsv1/issues"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*", "utils*"]

[tool.black]
line-length = 100
target-version = ['py311']

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
