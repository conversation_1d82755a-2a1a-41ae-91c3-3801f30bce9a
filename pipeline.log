2025-07-01 16:37:25,842 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-01 16:37:25,844 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-01 16:37:25,847 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-01 16:37:26,194 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-01 16:37:26,220 | INFO | patient_pipeline | Fetched 1 appointment records.
2025-07-01 16:37:26,262 | WARNING | patient_pipeline | No clinical data found for patient_id=26. Skipping.
2025-07-01 16:37:26,282 | INFO | patient_pipeline | Patient 26 (Appt 2008) has no clinical data and will be skipped.
2025-07-01 16:37:26,313 | INFO | patient_pipeline | Offset 0: no clinical data found for appointments, skipping.
2025-07-01 16:37:26,338 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-01 16:37:26,366 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-01 16:37:26,402 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-01 16:37:26,433 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-01 16:37:26,448 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-01 16:37:26,458 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-01 16:37:26,459 | INFO | patient_pipeline | Pipeline completed successfully. Processed 0 appointments total.
2025-07-01 16:53:03,062 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-01 16:53:03,063 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-01 16:53:03,076 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-01 16:53:03,461 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-01 16:53:03,516 | INFO | patient_pipeline | Fetched 1 appointment records.
2025-07-01 16:53:03,602 | INFO | patient_pipeline | Processing batch with 1 appointments using Gemini LLM
2025-07-01 16:53:03,603 | INFO | patient_pipeline | Building prompt for 1 patients and 160 campaign keywords
2025-07-01 16:53:03,604 | INFO | patient_pipeline | Reasoning required: True
2025-07-01 16:53:03,605 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 0: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-01 16:53:03,674 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-01 16:53:03,686 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-01 16:53:03,699 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-01 16:53:03,731 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-01 16:53:03,754 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-01 16:53:03,776 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-01 16:53:03,777 | INFO | patient_pipeline | Pipeline completed successfully. Processed 0 appointments total.
2025-07-01 16:57:18,188 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-01 16:57:18,188 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-01 16:57:18,190 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-01 16:57:18,566 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-01 16:57:18,588 | INFO | patient_pipeline | Fetched 1 appointment records.
2025-07-01 16:57:18,655 | INFO | patient_pipeline | Processing batch with 1 appointments using Gemini LLM
2025-07-01 16:57:18,656 | INFO | patient_pipeline | Building prompt for 1 patients and 160 campaign keywords
2025-07-01 16:57:18,656 | INFO | patient_pipeline | Reasoning required: False
2025-07-01 16:57:19,086 | INFO | patient_pipeline | Calling Gemini LLM...
2025-07-01 16:57:34,376 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-07-01 16:57:34,377 | INFO | patient_pipeline | Batch processed and validated.
2025-07-01 16:57:34,468 | INFO | patient_pipeline | Updating 1 appointment keyword results in database.
2025-07-01 16:57:34,499 | INFO | patient_pipeline | Update complete. 1 appointment records updated.
2025-07-01 16:57:34,520 | INFO | patient_pipeline | Processed 1 appointments (cumulative 1).
2025-07-01 16:57:34,555 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-01 16:57:34,578 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-01 16:57:34,598 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-01 16:57:34,623 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-01 16:57:34,646 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-01 16:57:34,675 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-01 16:57:34,676 | INFO | patient_pipeline | Pipeline completed successfully. Processed 1 appointments total.
2025-07-02 11:23:53,775 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:24:10,056 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:24:10,056 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 11:24:10,060 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 11:24:10,299 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 11:24:10,324 | INFO | patient_pipeline | Fetched 1 appointment records.
2025-07-02 11:24:10,400 | INFO | patient_pipeline | Processing batch with 1 appointments using Gemini LLM
2025-07-02 11:24:10,401 | INFO | patient_pipeline | Building prompt for 1 patients and 160 campaign keywords
2025-07-02 11:24:10,401 | INFO | patient_pipeline | Reasoning required: False
2025-07-02 11:24:10,555 | INFO | patient_pipeline | Calling Gemini LLM...
2025-07-02 11:24:18,072 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-07-02 11:24:18,073 | INFO | patient_pipeline | Batch processed and validated.
2025-07-02 11:24:18,115 | INFO | patient_pipeline | Updating 1 appointment keyword results in database.
2025-07-02 11:24:18,219 | ERROR | patient_pipeline | Error during appointment keyword update: ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Procedure or function sp_UpdateAppointmentKeywords has too many arguments specified. (8144) (SQLExecDirectW)')
2025-07-02 11:24:18,226 | INFO | patient_pipeline | Transaction rolled back successfully.
2025-07-02 11:25:44,067 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:25:44,067 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 11:25:44,069 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 11:25:44,253 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 11:25:44,262 | INFO | patient_pipeline | Fetched 1 appointment records.
2025-07-02 11:25:44,288 | WARNING | patient_pipeline | No clinical data found for patient_id=86. Skipping.
2025-07-02 11:25:44,304 | INFO | patient_pipeline | Patient 86 (Appt 2012) has no clinical data and will be skipped.
2025-07-02 11:25:44,314 | INFO | patient_pipeline | Offset 0: no clinical data found for appointments, skipping.
2025-07-02 11:25:44,336 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-02 11:25:44,347 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:25:44,356 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-02 11:25:44,373 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-02 11:25:44,388 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:25:44,401 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-02 11:25:44,401 | INFO | patient_pipeline | Pipeline completed successfully. Processed 0 appointments total.
2025-07-02 11:27:11,157 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:27:11,158 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 11:27:11,160 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 11:27:11,382 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 11:27:11,390 | INFO | patient_pipeline | Fetched 2 appointment records.
2025-07-02 11:27:11,439 | WARNING | patient_pipeline | No clinical data found for patient_id=86. Skipping.
2025-07-02 11:27:11,452 | INFO | patient_pipeline | Patient 86 (Appt 2012) has no clinical data and will be skipped.
2025-07-02 11:27:11,456 | INFO | patient_pipeline | Processing batch with 1 appointments using Gemini LLM
2025-07-02 11:27:11,456 | INFO | patient_pipeline | Building prompt for 1 patients and 160 campaign keywords
2025-07-02 11:27:11,456 | INFO | patient_pipeline | Reasoning required: False
2025-07-02 11:27:11,607 | INFO | patient_pipeline | Calling Gemini LLM...
2025-07-02 11:27:15,085 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-07-02 11:27:15,086 | INFO | patient_pipeline | Batch processed and validated.
2025-07-02 11:27:15,124 | INFO | patient_pipeline | Updating 1 appointment keyword results in database.
2025-07-02 11:27:15,150 | ERROR | patient_pipeline | Error during appointment keyword update: ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Error converting data type nvarchar to bigint. (8114) (SQLExecDirectW)')
2025-07-02 11:27:15,150 | INFO | patient_pipeline | Transaction rolled back successfully.
2025-07-02 11:28:19,152 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:28:19,152 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 11:28:19,154 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 11:28:19,426 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 11:28:19,482 | INFO | patient_pipeline | Fetched 2 appointment records.
2025-07-02 11:28:19,573 | WARNING | patient_pipeline | No clinical data found for patient_id=86. Skipping.
2025-07-02 11:28:19,588 | INFO | patient_pipeline | Patient 86 (Appt 2012) has no clinical data and will be skipped.
2025-07-02 11:28:19,593 | INFO | patient_pipeline | Processing batch with 1 appointments using Gemini LLM
2025-07-02 11:28:19,593 | INFO | patient_pipeline | Building prompt for 1 patients and 160 campaign keywords
2025-07-02 11:28:19,593 | INFO | patient_pipeline | Reasoning required: False
2025-07-02 11:28:19,716 | INFO | patient_pipeline | Calling Gemini LLM...
2025-07-02 11:28:22,956 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-07-02 11:28:22,957 | INFO | patient_pipeline | Batch processed and validated.
2025-07-02 11:28:22,966 | INFO | patient_pipeline | Updating 1 appointment keyword results in database.
2025-07-02 11:28:23,037 | INFO | patient_pipeline | Update complete. 1 appointment records updated.
2025-07-02 11:28:23,049 | INFO | patient_pipeline | Processed 1 appointments (cumulative 1).
2025-07-02 11:28:23,070 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-02 11:28:23,078 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:28:23,089 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-02 11:28:23,112 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-02 11:28:23,123 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:28:23,134 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-02 11:28:23,134 | INFO | patient_pipeline | Pipeline completed successfully. Processed 1 appointments total.
2025-07-02 11:30:23,636 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:30:23,636 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 11:30:23,637 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 11:30:23,822 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 11:30:23,852 | INFO | patient_pipeline | Fetched 2 appointment records.
2025-07-02 11:30:23,926 | INFO | patient_pipeline | Patient 26 (Appt 9999) clinical data unchanged. Skipping.
2025-07-02 11:30:23,931 | WARNING | patient_pipeline | No clinical data found for patient_id=86. Skipping.
2025-07-02 11:30:23,941 | INFO | patient_pipeline | Patient 86 (Appt 2012) has no clinical data and will be skipped.
2025-07-02 11:30:23,941 | INFO | patient_pipeline | Skipped 1 appointments with unchanged clinical data.
2025-07-02 11:30:23,946 | INFO | patient_pipeline | Offset 0: no clinical data found for appointments, skipping.
2025-07-02 11:30:23,962 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-02 11:30:23,972 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:30:23,984 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-02 11:30:23,998 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-02 11:30:24,010 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:30:24,020 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-02 11:30:24,021 | INFO | patient_pipeline | Pipeline completed successfully. Processed 0 appointments total.
2025-07-02 11:30:59,308 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 11:30:59,308 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 11:30:59,309 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 11:30:59,559 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 11:30:59,623 | INFO | patient_pipeline | Fetched 3 appointment records.
2025-07-02 11:30:59,662 | INFO | patient_pipeline | Patient 26 (Appt 9998) clinical data unchanged. Skipping.
2025-07-02 11:30:59,721 | INFO | patient_pipeline | Patient 26 (Appt 9999) clinical data unchanged. Skipping.
2025-07-02 11:30:59,726 | WARNING | patient_pipeline | No clinical data found for patient_id=86. Skipping.
2025-07-02 11:30:59,736 | INFO | patient_pipeline | Patient 86 (Appt 2012) has no clinical data and will be skipped.
2025-07-02 11:30:59,736 | INFO | patient_pipeline | Skipped 2 appointments with unchanged clinical data.
2025-07-02 11:30:59,743 | INFO | patient_pipeline | Offset 0: no clinical data found for appointments, skipping.
2025-07-02 11:30:59,765 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-02 11:30:59,896 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:30:59,906 | INFO | patient_pipeline | No appointments found at offset 25. Trying next batch...
2025-07-02 11:30:59,919 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-02 11:30:59,929 | INFO | patient_pipeline | Fetched 0 appointment records.
2025-07-02 11:30:59,938 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-02 11:30:59,939 | INFO | patient_pipeline | Pipeline completed successfully. Processed 0 appointments total.
