2025-07-01 13:45:37,256 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-01 13:45:37,257 | INFO | patient_pipeline | 🚀 Starting Patient Keyword Pipeline – hash-aware mode
2025-07-01 13:45:37,268 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-01 13:45:38,606 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=0, batch=25
2025-07-01 13:45:40,977 | INFO | patient_pipeline | Fetched 6 patient IDs.
2025-07-01 13:45:41,041 | WARNING | patient_pipeline | No clinical data found for patient_id=1 (from Appointment table). Skipping.
2025-07-01 13:45:41,043 | INFO | patient_pipeline | Patient 1 has no data and will be skipped from processing batch.
2025-07-01 13:45:43,625 | INFO | patient_pipeline | Processing batch with 1 patients using Gemini LLM
2025-07-01 13:45:43,626 | INFO | patient_pipeline | Building prompt for 1 patients and 160 campaign keywords
2025-07-01 13:45:43,626 | INFO | patient_pipeline | Reasoning required: True
2025-07-01 13:45:44,344 | INFO | patient_pipeline | Calling Gemini LLM...
2025-07-01 13:46:08,523 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-07-01 13:46:08,525 | WARNING | patient_pipeline | Patient 11: expected 7 valid keywords after filtering, got 6
2025-07-01 13:46:08,526 | WARNING | patient_pipeline | Found 1 invalid keywords in batch: [('11', ['dizziness'])]
2025-07-01 13:46:08,526 | INFO | patient_pipeline | Batch processed and validated.
2025-07-01 13:46:08,550 | INFO | patient_pipeline | Upserting 1 patient results to database.
2025-07-01 13:46:08,598 | INFO | patient_pipeline | Upsert complete.
2025-07-01 13:46:08,599 | INFO | patient_pipeline | Processed 1 changed patients (cumulative 1).
2025-07-01 13:46:08,602 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=25, batch=25
2025-07-01 13:46:08,609 | INFO | patient_pipeline | Fetched 0 patient IDs.
2025-07-01 13:46:08,612 | INFO | patient_pipeline | No patients found at offset 25. Trying next batch...
2025-07-01 13:46:08,620 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=50, batch=25
2025-07-01 13:46:08,632 | INFO | patient_pipeline | Fetched 0 patient IDs.
2025-07-01 13:46:08,634 | WARNING | patient_pipeline | Reached maximum consecutive empty batches (2). Exiting to prevent infinite loop.
2025-07-01 13:46:08,635 | INFO | patient_pipeline | Pipeline completed successfully. Processed 1 patients total.
