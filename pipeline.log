2025-07-02 12:10:45,471 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 12:10:45,471 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 12:10:45,472 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 12:10:45,642 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 12:10:45,660 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:46,588 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:46,589 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:46,593 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:46,593 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 0: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:46,606 | INFO | patient_pipeline | Fetching appointment records: offset=25, batch=25
2025-07-02 12:10:46,623 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:47,460 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:47,460 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:47,460 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:47,462 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 25: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:47,476 | INFO | patient_pipeline | Fetching appointment records: offset=50, batch=25
2025-07-02 12:10:47,487 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:48,696 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:48,696 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:48,697 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:48,697 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 50: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:48,720 | INFO | patient_pipeline | Fetching appointment records: offset=75, batch=25
2025-07-02 12:10:48,732 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:49,658 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:49,658 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:49,658 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:49,659 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 75: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:49,674 | INFO | patient_pipeline | Fetching appointment records: offset=100, batch=25
2025-07-02 12:10:49,694 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:50,794 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:50,794 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:50,794 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:50,795 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 100: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:50,813 | INFO | patient_pipeline | Fetching appointment records: offset=125, batch=25
2025-07-02 12:10:50,828 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:51,916 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:51,916 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:51,916 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:51,917 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 125: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:51,929 | INFO | patient_pipeline | Fetching appointment records: offset=150, batch=25
2025-07-02 12:10:51,944 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:53,234 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:53,235 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:53,235 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:53,235 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 150: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:53,252 | INFO | patient_pipeline | Fetching appointment records: offset=175, batch=25
2025-07-02 12:10:53,263 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:54,273 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:54,273 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:54,273 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:54,273 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 175: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:54,289 | INFO | patient_pipeline | Fetching appointment records: offset=200, batch=25
2025-07-02 12:10:54,304 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:55,671 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:55,672 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:55,672 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:55,673 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 200: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:55,688 | INFO | patient_pipeline | Fetching appointment records: offset=225, batch=25
2025-07-02 12:10:55,699 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:57,296 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:57,296 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:57,296 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:57,297 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 225: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:57,314 | INFO | patient_pipeline | Fetching appointment records: offset=250, batch=25
2025-07-02 12:10:57,326 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:58,544 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:58,544 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:58,544 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:58,545 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 250: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:58,563 | INFO | patient_pipeline | Fetching appointment records: offset=275, batch=25
2025-07-02 12:10:58,577 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:10:59,587 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:10:59,587 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:10:59,587 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:10:59,588 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 275: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:10:59,600 | INFO | patient_pipeline | Fetching appointment records: offset=300, batch=25
2025-07-02 12:10:59,617 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:11:00,657 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:11:00,657 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:11:00,657 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:11:00,658 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 300: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:11:00,674 | INFO | patient_pipeline | Fetching appointment records: offset=325, batch=25
2025-07-02 12:11:00,687 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:11:01,752 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:11:01,752 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:11:01,752 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:11:01,752 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 325: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:11:01,765 | INFO | patient_pipeline | Fetching appointment records: offset=350, batch=25
2025-07-02 12:11:01,783 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:11:02,570 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:11:02,570 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:11:02,570 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:11:02,571 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 350: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:11:02,584 | INFO | patient_pipeline | Fetching appointment records: offset=375, batch=25
2025-07-02 12:11:02,601 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:11:03,687 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:11:03,687 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:11:03,688 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:11:03,688 | ERROR | patient_pipeline | Unexpected error during LLM processing at offset 375: Object of type datetime is not JSON serializable. Skipping batch.
2025-07-02 12:11:03,700 | INFO | patient_pipeline | Fetching appointment records: offset=400, batch=25
2025-07-02 12:11:03,717 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:11:03,760 | INFO | patient_pipeline | Received signal 2. Requesting graceful shutdown...
2025-07-02 12:11:04,538 | INFO | patient_pipeline | Shutdown requested. Stopping before LLM processing.
2025-07-02 12:11:04,538 | INFO | patient_pipeline | Pipeline stopped gracefully due to shutdown request. Processed 0 appointments total.
