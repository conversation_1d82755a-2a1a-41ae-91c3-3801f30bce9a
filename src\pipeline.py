"""
Main pipeline module for processing appointment data and generating campaign keywords.

This module orchestrates the entire appointment keyword extraction pipeline:
1. Loads campaign keywords from CSV
2. Fetches appointment data from database in batches
3. Retrieves clinical data for each patient
4. Generates keywords using LLM analysis
5. Updates keywords in the appointment table using stored procedures

The pipeline processes appointments from the dbo.Appointment_EducationalContent_Keyword table
and updates the Keyword column with generated keywords.
"""

import logging
import json
import signal
import sys
import config
from src import db
from src import llm
from utils import hashing
from utils import validation
import os
import warnings
warnings.filterwarnings("ignore", message="pandas only supports SQLAlchemy connectable")

# Setup logging to both file and console
logging.basicConfig(
    level=config.LOG_LEVEL,
    format="%(asctime)s | %(levelname)s | %(name)s | %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(config.LOG_FILE, mode="a", encoding="utf-8")
    ]
)
logger = logging.getLogger("patient_pipeline")

# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    global shutdown_requested
    logger.info(f"Received signal {signum}. Requesting graceful shutdown...")
    shutdown_requested = True

# Register signal handlers for graceful shutdown
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

logger.info(f"Logging initialized. Writing to {os.path.abspath(config.LOG_FILE)}")

def process_batch(
    appointment_json,
    campaign,
):
    """
    Process a batch of appointments through Gemini LLM to generate campaign keywords.

    This function takes a batch of appointment data and generates campaign keywords
    for each appointment using Google's Gemini LLM service. It includes token counting,
    cost estimation, and response validation.

    Args:
        appointment_json (Dict[str, Any]): Dictionary mapping appointment keys to their clinical data
        campaign (List[str]): List of valid campaign keywords to choose from

    Returns:
        Dict[str, Any]: Parsed and validated LLM response containing keywords for each appointment

    Raises:
        RuntimeError: If token or cost limits are exceeded
        Exception: If LLM call fails or response validation fails

    Example:
        >>> appt_data = {"12345_67890": {"Results": [...], "Problems": [...]}}
        >>> keywords = ["diabetes", "hypertension", "asthma"]
        >>> result = process_batch(appt_data, keywords)
    """
    logger.info(f"Processing batch with {len(appointment_json)} appointments using Gemini LLM")

    # Build the prompt with appointment data and campaign keywords
    prompt = llm.build_prompt(appointment_json, campaign)

    # Count tokens and check limits before making expensive LLM call
    in_tokens = hashing.count_tokens(prompt)
    llm.check_run_limits(in_tokens, out_tok_est=8_000)

    # Make the Gemini LLM call
    logger.info("Calling Gemini LLM...")
    raw_response = llm.query_gemini(prompt)

    # Parse and validate the response
    logger.info("LLM call complete. Parsing response...")
    parsed = validation.validate_and_parse(raw_response, campaign)
    logger.info("Batch processed and validated.")

    return parsed

def main():
    """
    Main pipeline function that orchestrates the entire keyword extraction process.

    This function implements the core pipeline logic:
    1. Loads campaign keywords from CSV file
    2. Processes appointments in batches with pagination
    3. Retrieves clinical data for each patient
    4. Generates keywords using LLM for all appointments
    5. Updates keywords in the appointment table using stored procedures

    The pipeline processes all appointments in the dbo.Appointment_EducationalContent_Keyword table.

    Raises:
        FileNotFoundError: If campaign keywords CSV file is not found
        Exception: If database connection or LLM processing fails

    Example:
        >>> main()  # Processes all appointments in the database
    """
    logger.info("🚀 Starting Appointment Keyword Pipeline")

    # Load campaign keywords from CSV file
    csv_file = config.CAMPAIGN_KEYWORDS_CSV
    campaign_kw = []
    try:
        with open(csv_file, newline="", encoding="utf-8") as f:
            import csv
            rdr = csv.reader(f)
            # Skip the first row (header)
            next(rdr, None)

            # Collect all non-empty cells from all rows and columns
            seen_keywords = set()
            for row in rdr:
                for cell in row:
                    # Clean and normalize the keyword
                    keyword = cell.strip().lower()
                    if keyword and keyword not in seen_keywords:
                        campaign_kw.append(keyword)
                        seen_keywords.add(keyword)

        if not campaign_kw:
            raise ValueError("No valid campaign keywords found in CSV file")

        logger.info("Loaded %d unique campaign keywords.", len(campaign_kw))
        logger.debug("Sample keywords: %s", campaign_kw[:10])

    except FileNotFoundError:
        logger.error("Campaign keywords CSV file not found: %s", csv_file)
        raise
    except Exception as e:
        logger.error("Error loading campaign keywords from CSV: %s", e)
        raise

    # Initialize pagination and tracking variables
    offset = 0
    total_processed = 0
    consecutive_empty_batches = 0
    max_empty_batches = 2

    # Main processing loop - continues until all appointments are processed
    while not shutdown_requested:
        try:
            # Fetch the next batch of appointment records
            with db.sql_connection() as conn:
                db.ensure_tables(conn)  # Verify database tables exist
                appointments = db.fetch_appointment_batch(conn, offset, batch=config.BATCH_SIZE)

            # Exit loop if no more appointments to process
            if not appointments:
                consecutive_empty_batches += 1
                if consecutive_empty_batches >= max_empty_batches:
                    logger.warning("Reached maximum consecutive empty batches (%d). Exiting to prevent infinite loop.", max_empty_batches)
                    break
                logger.info("No appointments found at offset %d. Trying next batch...", offset)
                offset += config.BATCH_SIZE
                continue
            else:
                consecutive_empty_batches = 0  # Reset counter when we find appointments

        except Exception as e:
            logger.error("Error fetching appointments at offset %d: %s", offset, e)
            offset += config.BATCH_SIZE
            continue

        # Fetch detailed clinical data for each appointment
        # Enable hash-based change detection to skip unchanged patients
        with db.sql_connection() as conn:
            appointment_data = db.fetch_batch_appointment_data(conn, appointments, skip_unchanged=True)

        # Skip this batch if no clinical data found
        if not appointment_data:
            logger.info("Offset %d: no clinical data found for appointments, skipping.", offset)
            offset += config.BATCH_SIZE
            continue

        # Check for shutdown before expensive LLM processing
        if shutdown_requested:
            logger.info("Shutdown requested. Stopping before LLM processing.")
            break

        # Process appointments through LLM with specific error handling
        try:
            results = process_batch(appointment_data, campaign_kw)
        except (ConnectionError, TimeoutError) as e:
            logger.error("Network/timeout error during LLM processing at offset %d: %s. Will retry next run.", offset, e)
            offset += config.BATCH_SIZE
            continue
        except ValueError as e:
            logger.error("Data validation error during LLM processing at offset %d: %s. Skipping batch.", offset, e)
            offset += config.BATCH_SIZE
            continue
        except Exception as e:
            logger.error("Unexpected error during LLM processing at offset %d: %s. Skipping batch.", offset, e)
            offset += config.BATCH_SIZE
            continue

        # Map LLM results back to appointment data and update database
        with db.sql_connection() as conn:
            db.update_appointment_keywords(conn, results, appointment_data)

        # Update progress tracking
        total_processed += len(appointment_data)
        logger.info("Processed %d appointments (cumulative %d).", len(appointment_data), total_processed)
        offset += config.BATCH_SIZE

        # Check for shutdown after processing
        if shutdown_requested:
            logger.info("Shutdown requested. Stopping after processing current batch.")
            break

    # Pipeline completion summary
    if shutdown_requested:
        logger.info("Pipeline stopped gracefully due to shutdown request. Processed %d appointments total.", total_processed)
    else:
        logger.info("Pipeline completed successfully. Processed %d appointments total.", total_processed)