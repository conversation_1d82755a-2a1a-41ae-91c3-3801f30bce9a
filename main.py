"""
Main entry point for the Campaign Keywords Pipeline.

This module serves as the entry point for the patient keyword extraction pipeline.
It imports and executes the main pipeline function that processes patient data
and generates campaign keywords using LLM analysis.

Usage:
    python main.py

The pipeline will:
1. Load campaign keywords from CSV
2. Fetch patient data from the database in batches
3. Use hash-based change detection to process only modified patients
4. Generate keywords using LLM (Gemini)
5. Store results in the database
"""

from src.pipeline import main

if __name__ == "__main__":
    # Execute the main pipeline process
    main()
