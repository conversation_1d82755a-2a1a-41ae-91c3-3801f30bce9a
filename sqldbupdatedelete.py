import sys
sys.path.append('src')
import db

# List of PatientIDs (deduplicated)
patient_ids = [
    25, 50, 90, 13, 117, 142, 177, 193, 110, 58, 258, 268, 267, 209, 277, 27, 355, 367, 368, 178, 202, 386, 56, 120, 106, 95, 394, 103, 418, 397, 417, 354, 179, 89, 255, 24, 104, 263, 124, 423, 54, 196, 517, 44, 26
]
# Remove duplicates and sort
patient_ids = sorted(set(patient_ids))

print('Deleting all records from dbo.Appointment_EducationalContent_Keyword...')
try:
    with db.sql_connection() as conn:
        cursor = conn.cursor()
        # Delete all rows
        cursor.execute('DELETE FROM dbo.Appointment_EducationalContent_Keyword')
        conn.commit()
        print('✓ All records deleted.')

        # Insert new records for the specified PatientIDs
        print(f'Inserting appointments for {len(patient_ids)} PatientIDs...')
        # Prepare the PatientIDs for SQL IN clause
        ids_str = ','.join(str(pid) for pid in patient_ids)
        insert_query = f'''
        INSERT INTO dbo.Appointment_EducationalContent_Keyword 
        (OrganizationID, PatientID, ApptID, ApptNo, CreatedBy, CreatedUTCDTTM, ModifiedBy, ModifiedUTCDTTM)
        SELECT
            ISNULL(a.OrganizationId, 1) as OrganizationID,
            a.UserID as PatientID,
            a.AppointmentId as ApptID,
            a.AppNo as ApptNo,
            1 as CreatedBy,
            GETDATE() as CreatedUTCDTTM,
            1 as ModifiedBy,
            GETDATE() as ModifiedUTCDTTM
        FROM dbo.Appointments a
        WHERE a.UserID IN ({ids_str})
          AND a.UserID IS NOT NULL 
          AND a.AppNo IS NOT NULL
        '''
        cursor.execute(insert_query)
        rows_inserted = cursor.rowcount
        conn.commit()
        print(f'✓ Successfully inserted {rows_inserted} new appointment records.')

        # Show a sample of the new records
        print('\nSample of newly inserted records:')
        cursor.execute('''
            SELECT TOP 10
                PatientID, 
                ApptID, 
                ApptNo, 
                CreatedUTCDTTM
            FROM dbo.Appointment_EducationalContent_Keyword
            ORDER BY ApptID DESC
        ''')
        new_records = cursor.fetchall()
        for record in new_records:
            patient_id, appt_id, appt_no, created = record
            print(f'   Patient {patient_id}, Appt {appt_id} ({appt_no}), Created: {created}')

except Exception as e:
    print('Error:', str(e))
    import traceback
    traceback.print_exc()