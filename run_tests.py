#!/usr/bin/env python3
"""
Test runner for Campaign Keywords Pipeline fixes.
"""
import sys
import os
import subprocess
import importlib.util

def check_pytest_installed():
    """Check if pytest is installed."""
    try:
        import pytest
        return True
    except ImportError:
        return False

def install_pytest():
    """Install pytest if not available."""
    print("Installing pytest...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytest"])
        return True
    except subprocess.CalledProcessError:
        print("Failed to install pytest")
        return False

def run_tests():
    """Run all tests."""
    if not check_pytest_installed():
        if not install_pytest():
            print("Cannot run tests without pytest")
            return False
    
    # Import pytest after ensuring it's installed
    import pytest
    
    # Run tests with verbose output
    test_args = [
        "tests/",
        "-v",
        "--tb=short",
        "-x"  # Stop on first failure for easier debugging
    ]
    
    print("Running tests for Campaign Keywords Pipeline fixes...")
    print("=" * 60)
    
    result = pytest.main(test_args)
    
    if result == 0:
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("All fixes are working correctly.")
    else:
        print("\n" + "=" * 60)
        print("❌ SOME TESTS FAILED")
        print("Check the output above for details.")
    
    return result == 0

def run_individual_test_files():
    """Run each test file individually for detailed results."""
    test_files = [
        "tests/test_csv_parsing.py",
        "tests/test_token_counting.py", 
        "tests/test_database.py",
        "tests/test_graceful_shutdown.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n{'='*60}")
            print(f"Running {test_file}")
            print('='*60)
            
            try:
                import pytest
                result = pytest.main([test_file, "-v", "--tb=short"])
                results[test_file] = result == 0
            except Exception as e:
                print(f"Error running {test_file}: {e}")
                results[test_file] = False
        else:
            print(f"Test file not found: {test_file}")
            results[test_file] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    for test_file, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_file}: {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall result: {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    # Add project root to Python path
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    print("Campaign Keywords Pipeline - Test Suite")
    print("Testing all implemented fixes...")
    
    # Run individual test files for detailed output
    success = run_individual_test_files()
    
    sys.exit(0 if success else 1)
