"""
Test database connection and transaction improvements.
"""
import pytest
import sys
import os
from unittest.mock import patch, MagicMock, call
import pyodbc
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from src.db import sql_connection, upsert_patient_keywords


class TestDatabaseImprovements:
    """Test database connection and transaction improvements."""
    
    def test_database_connection_retry_logic(self):
        """Test that database connections retry with exponential backoff."""
        with patch('pyodbc.connect') as mock_connect:
            # Mock connection failures then success
            mock_connect.side_effect = [
                pyodbc.Error("Connection failed"),
                pyodbc.<PERSON>rror("Connection failed again"),
                MagicMock()  # Success on third try
            ]
            
            with patch('time.sleep') as mock_sleep:
                with patch('src.db.logger') as mock_logger:
                    with sql_connection() as conn:
                        assert conn is not None
                    
                    # Should have retried 2 times before success
                    assert mock_connect.call_count == 3
                    
                    # Should have slept with exponential backoff (1s, 2s)
                    expected_sleep_calls = [call(1), call(2)]
                    mock_sleep.assert_has_calls(expected_sleep_calls)
                    
                    # Should have logged warnings
                    assert mock_logger.warning.call_count == 2
    
    def test_database_connection_max_retries_exceeded(self):
        """Test that connection fails after max retries."""
        with patch('pyodbc.connect') as mock_connect:
            # Mock all connection attempts to fail
            mock_connect.side_effect = pyodbc.Error("Persistent connection failure")
            
            with patch('time.sleep'):
                with patch('src.db.logger') as mock_logger:
                    with pytest.raises(pyodbc.Error):
                        with sql_connection():
                            pass
                    
                    # Should have tried 3 times
                    assert mock_connect.call_count == 3
                    
                    # Should have logged final error
                    mock_logger.error.assert_called_once()
    
    def test_database_connection_timeout_increased(self):
        """Test that database connection timeout is increased to 60 seconds."""
        with patch('pyodbc.connect') as mock_connect:
            mock_connect.return_value = MagicMock()
            
            with sql_connection():
                pass
            
            # Check that connect was called with timeout=60
            args, kwargs = mock_connect.call_args
            conn_str = args[0]
            timeout = kwargs.get('timeout', None)
            assert timeout == 60
    
    def test_server_name_validation(self):
        """Test that server names are validated to prevent injection."""
        # Test valid server names
        valid_servers = [
            "localhost",
            "server.domain.com",
            "SERVER-01",
            "*************",
            "server_name"
        ]
        
        for server in valid_servers:
            with patch.object(config, 'DB_SERVER', server):
                with patch('pyodbc.connect') as mock_connect:
                    mock_connect.return_value = MagicMock()
                    try:
                        with sql_connection():
                            pass
                        # Should not raise exception
                    except ValueError:
                        pytest.fail(f"Valid server name {server} was rejected")
        
        # Test invalid server names
        invalid_servers = [
            "server; DROP TABLE users;",
            "server' OR '1'='1",
            "server\nmalicious",
            "server\tmalicious"
        ]
        
        for server in invalid_servers:
            with patch.object(config, 'DB_SERVER', server):
                with pytest.raises(ValueError, match="Invalid server name format"):
                    with sql_connection():
                        pass
    
    def test_date_validation(self):
        """Test that appointment start date is validated."""
        from src.db import fetch_patient_ids
        
        # Test valid date formats
        valid_dates = ["2023-01-01", "2024-12-31"]
        
        for date in valid_dates:
            with patch.object(config, 'APPOINTMENT_START_DATE', date):
                with patch('pyodbc.connect') as mock_connect:
                    mock_conn = MagicMock()
                    mock_cursor = MagicMock()
                    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
                    mock_cursor.fetchall.return_value = []
                    mock_connect.return_value = mock_conn
                    
                    try:
                        fetch_patient_ids(mock_conn, 0, 10)
                        # Should not raise exception
                    except ValueError:
                        pytest.fail(f"Valid date {date} was rejected")
        
        # Test invalid date formats
        invalid_dates = [
            "2023/01/01",
            "01-01-2023",
            "2023-1-1",
            "invalid-date",
            "2023-01-01; DROP TABLE patients;"
        ]
        
        for date in invalid_dates:
            with patch.object(config, 'APPOINTMENT_START_DATE', date):
                with patch('pyodbc.connect') as mock_connect:
                    mock_conn = MagicMock()
                    mock_connect.return_value = mock_conn
                    
                    with pytest.raises(ValueError, match="Invalid date format"):
                        fetch_patient_ids(mock_conn, 0, 10)
    
    def test_transaction_rollback_on_error(self):
        """Test that transactions are rolled back on error."""
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        
        # Mock executemany to raise an exception
        mock_cursor.executemany.side_effect = Exception("Database error")
        
        test_data = [
            {
                "patient_id": "123",
                "keywords": ["diabetes", "insulin"],
                "reasoning": {"condition": "diabetes"},
                "data_hash": "abc123"
            }
        ]
        
        with patch('src.db.logger') as mock_logger:
            with pytest.raises(Exception, match="Database error"):
                upsert_patient_keywords(mock_conn, test_data)
            
            # Should have attempted rollback
            mock_conn.rollback.assert_called_once()
            
            # Should have logged the error and rollback
            assert mock_logger.error.call_count >= 1
            assert mock_logger.info.call_count >= 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
