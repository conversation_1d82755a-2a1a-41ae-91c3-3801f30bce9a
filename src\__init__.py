"""
Source package for the Campaign Keywords Pipeline.

This package contains the core modules for processing patient data
and generating campaign keywords using Large Language Models:

- pipeline: Main orchestration logic and batch processing
- db: Database operations and patient data retrieval
- llm: LLM integration for keyword generation

The pipeline processes patient clinical data in batches, uses hash-based
change detection for efficiency, and generates exactly 7 campaign keywords
per patient with clinical reasoning.
"""