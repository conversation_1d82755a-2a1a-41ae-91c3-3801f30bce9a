-- Stored Procedures for Campaign Keywords Pipeline
-- Database: V_820_Dev
-- Table: dbo.Appointment_EducationalContent_Keyword

-- =============================================
-- Stored Procedure: sp_GetAppointmentBatch
-- Description: Fetch a batch of appointment records with pagination
-- Parameters: 
--   @Offset: Number of records to skip
--   @BatchSize: Number of records to return
--   @StartDate: Optional start date filter (can be NULL)
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetAppointmentBatch
    @Offset INT,
    @BatchSize INT,
    @StartDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT 
        PatientID,
        ApptID,
        ApptNo,
        ApptDate
    FROM dbo.Appointment_EducationalContent_Keyword
    WHERE (@StartDate IS NULL OR CAST(ApptDate AS DATE) >= CAST(@StartDate AS DATE))
    ORDER BY PatientID, ApptID
    OFFSET @Offset ROWS 
    FETCH NEXT @BatchSize ROWS ONLY;
END
GO

-- =============================================
-- Stored Procedure: sp_GetPatientClinicalData
-- Description: Fetch comprehensive clinical data for a single patient
-- Parameters:
--   @PatientID: Patient identifier
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetPatientClinicalData
    @PatientID BIGINT
AS
BEGIN
    SET NOCOUNT ON;

    -- Get all clinical data for the patient using separate queries
    -- Results
    SELECT
        'Result' as DataType,
        ResultName as Name,
        ObservationValue as Value1,
        ObservationUnit as Value2,
        NULL as Value3
    FROM dbo.Patient_Result
    WHERE PatientID = @PatientID

    UNION ALL

    -- Problems
    SELECT
        'Problem' as DataType,
        ProblemName as Name,
        Type as Value1,
        DetailText as Value2,
        NULL as Value3
    FROM dbo.Patient_Problem
    WHERE PatientID = @PatientID

    UNION ALL

    -- Medications
    SELECT
        'Medication' as DataType,
        MedicationName as Name,
        MedType as Value1,
        CAST(DoseQuantity as NVARCHAR(50)) as Value2,
        NULL as Value3
    FROM dbo.Patient_Medication
    WHERE PatientID = @PatientID

    UNION ALL

    -- Allergies
    SELECT
        'Allergy' as DataType,
        AllergyName as Name,
        NULL as Value1,
        NULL as Value2,
        NULL as Value3
    FROM dbo.Patient_Allergy
    WHERE PatientID = @PatientID;
END
GO

-- =============================================
-- Stored Procedure: sp_UpdateAppointmentKeywords
-- Description: Update keywords for specific appointment records
-- Parameters: 
--   @PatientID: Patient identifier
--   @ApptID: Appointment identifier
--   @Keywords: Generated keywords (JSON or comma-separated)
--   @Source: Source of keywords (e.g., 'LLM')
--   @KeywordSource: Source system (e.g., 'Gemini')
--   @ModifiedBy: User ID who modified the record
-- =============================================
CREATE OR ALTER PROCEDURE sp_UpdateAppointmentKeywords
    @PatientID BIGINT,
    @ApptID BIGINT,
    @Keywords NVARCHAR(2000),
    @Source NVARCHAR(10) = 'LLM',
    @KeywordSource NVARCHAR(25) = 'Gemini',
    @ModifiedBy BIGINT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE dbo.Appointment_EducationalContent_Keyword
    SET 
        [Keyword] = @Keywords,
        [Source] = @Source,
        [KeywordSource] = @KeywordSource,
        [ModifiedBy] = @ModifiedBy,
        [ModifiedUTCDTTM] = GETUTCDATE()
    WHERE PatientID = @PatientID 
      AND ApptID = @ApptID;
      
    -- Return number of rows affected
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- =============================================
-- Stored Procedure: sp_BatchUpdateAppointmentKeywords
-- Description: Update keywords for multiple appointment records in a batch
-- Parameters: 
--   @KeywordUpdates: Table-valued parameter containing batch updates
-- =============================================
-- First create the table type for batch updates
CREATE TYPE KeywordUpdateType AS TABLE
(
    PatientID BIGINT,
    ApptID BIGINT,
    Keywords NVARCHAR(2000),
    Source NVARCHAR(10),
    KeywordSource NVARCHAR(25)
);
GO

CREATE OR ALTER PROCEDURE sp_BatchUpdateAppointmentKeywords
    @KeywordUpdates KeywordUpdateType READONLY,
    @ModifiedBy BIGINT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE aec
    SET 
        [Keyword] = ku.Keywords,
        [Source] = ku.Source,
        [KeywordSource] = ku.KeywordSource,
        [ModifiedBy] = @ModifiedBy,
        [ModifiedUTCDTTM] = GETUTCDATE()
    FROM dbo.Appointment_EducationalContent_Keyword aec
    INNER JOIN @KeywordUpdates ku ON aec.PatientID = ku.PatientID 
                                  AND aec.ApptID = ku.ApptID;
    
    -- Return number of rows affected
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- =============================================
-- Stored Procedure: sp_GetAppointmentsByPatientIDs
-- Description: Get appointment records for specific patient IDs
-- Parameters: 
--   @PatientIDs: Comma-separated list of patient IDs
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetAppointmentsByPatientIDs
    @PatientIDs NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Split the comma-separated patient IDs and convert to table
    WITH PatientIDList AS (
        SELECT CAST(value AS BIGINT) AS PatientID
        FROM STRING_SPLIT(@PatientIDs, ',')
        WHERE value != ''
    )
    SELECT 
        aec.PatientID,
        aec.ApptID,
        aec.ApptNo,
        aec.ApptDate,
        aec.[Keyword] as ExistingKeywords
    FROM dbo.Appointment_EducationalContent_Keyword aec
    INNER JOIN PatientIDList pil ON aec.PatientID = pil.PatientID
    ORDER BY aec.PatientID, aec.ApptID;
END
GO
